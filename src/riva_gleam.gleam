import gleam/int
import lustre
import lustre/element.{type Element}
import lustre/element/html
import lustre/event

// TYPES -----------------------------------------------------------------------

pub type Model =
  Int

pub type Msg {
  Increment
  Decrement
}

// INIT ------------------------------------------------------------------------

fn init(_flags) -> Model {
  0
}

// UPDATE ----------------------------------------------------------------------

fn update(model: Model, msg: Msg) -> Model {
  case msg {
    Increment -> model + 1
    Decrement -> model - 1
  }
}

// VIEW ------------------------------------------------------------------------

fn view(model: Model) -> Element(Msg) {
  let count = model |> int.to_string

  html.div([], [
    html.h1([], [element.text("Riva Gleam Counter")]),
    html.p([], [element.text("Count: " <> count)]),
    html.button([event.on_click(Increment)], [element.text("+")]),
    html.button([event.on_click(Decrement)], [element.text("-")]),
  ])
}

// MAIN ------------------------------------------------------------------------

pub fn main() -> Nil {
  let app = lustre.simple(init, update, view)
  let assert Ok(_) = lustre.start(app, "#app", Nil)
  Nil
}
